# OCS Inventory 前端管理系统

基于 Vue 3 + Element Plus 构建的 OCS Inventory 设备管理前端界面。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **UI 库**: Element Plus
- **HTTP 请求**: Axios
- **构建工具**: Vite

## 功能特性

- 📊 设备列表展示和分页
- 🔍 设备搜索和筛选
- 📱 设备详情查看
- 💾 已安装软件列表
- 📈 仪表盘统计信息
- 🎨 响应式设计

## 项目结构

```
src/
├── components/          # 通用组件
│   ├── Pagination.vue   # 分页组件
│   └── SoftwareList.vue # 软件列表组件
├── layouts/             # 布局组件
│   └── MainLayout.vue   # 主布局
├── router/              # 路由配置
│   └── index.js
├── services/            # API 服务
│   └── api.js
├── stores/              # Pinia 状态管理
│   └── computerStore.js
├── views/               # 页面组件
│   ├── ComputerListView.vue    # 设备列表页
│   ├── ComputerDetailView.vue  # 设备详情页
│   └── DashboardView.vue       # 仪表盘页
├── App.vue
├── main.js
└── style.css
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## API 配置

项目通过 Vite 代理配置连接后端 API：

- 开发环境：`http://localhost:3000` (前端) → `http://localhost:5168` (后端)
- API 前缀：`/api`

## 主要页面

### 1. 仪表盘 (`/`)
- 设备统计信息
- 最近更新的设备列表
- 快速操作入口

### 2. 设备列表 (`/computers`)
- 分页显示所有设备
- 搜索和筛选功能
- 点击查看设备详情

### 3. 设备详情 (`/computer/:id`)
- 设备基本信息
- 硬件配置详情
- 已安装软件列表

## 开发注意事项

1. 确保后端 API 服务运行在 `http://localhost:5168`
2. 所有 API 调用都通过 `services/api.js` 统一管理
3. 使用 Pinia 进行状态管理，避免组件间直接传递复杂数据
4. 遵循 Vue 3 Composition API 最佳实践

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88
