using Flurl.Http;
using Microsoft.AspNetCore.Http;
using OCSInventoryDotnetServer.Entity;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// 添加CORS服务
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins("http://localhost:5173", "http://localhost:3000", "http://localhost:8080")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});


var app = builder.Build();
var baseUrl = app.Configuration.GetValue<string>("OcsUrl");
var ocsUser = app.Configuration.GetValue<string>("OcsUser");
var ocsPwd = app.Configuration.GetValue<string>("OcsPasswd");

// 启用CORS
app.UseCors("AllowFrontend");

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

//app.UseHttpsRedirection();



app.MapGet("/devices", async () =>
{
    var url= $"{baseUrl}/computers/listID";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<List<DeviceId>>();
     
    return resp.Select(x=>x.Id);
})
.WithDescription("获取设备id'")
.WithOpenApi(operation => {
    operation.Summary = "获取所有设备ID";

    return operation;
});


app.MapGet("/ListOneComputerDetails", async (int id) =>
{
    var url = $"{baseUrl}/computer/{id}";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<ComputerDetailsResponse>();

    return resp;
})
.WithDescription("根据id获取计算机详细信息")
.WithOpenApi(operation => {
    operation.Summary = "根据id获取计算机详细信息";
    operation.Description = "根据id获取计算机详细信息，包括硬件、软件、网络等完整信息";
    
    return operation;
});


app.MapGet("/ListComputersDetails", async (int start, int limit) =>
{
    var url = $"{baseUrl}/computers?start={start}&limit={limit}";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<ComputerDetailsResponse>();

    return resp;
})
.WithDescription("获取指定范围内的计算机详细信息")
.WithOpenApi(operation => {
    operation.Summary = "获取指定范围内的计算机详细信息";
    operation.Description = "获取指定范围内的计算机详细信息，包括硬件、软件、网络等完整信息";
    
    return operation;
});

app.MapGet("/ListLastUpdatedComputers", async (long timestamp) =>
{
    var url = $"{baseUrl}/computers/lastupdate/{timestamp}";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<DeviceId>();

    return resp;
})
.WithDescription("列出最后更新的计算机id")
.WithOpenApi(operation => {
    operation.Summary = "列出最后更新的计算机id";
    operation.Description = "列出最后更新的计算机id";
    
    return operation;
});

app.MapGet("/Softwares", async (int computerId) =>
{
    var url = $"{baseUrl}/computer/{computerId}/software";
    var resp = await url
     .WithBasicAuth(ocsUser, ocsPwd)
     .GetJsonAsync<SoftwareInfoResponse>();

    // Check if response has data for the requested computer ID
    if (resp.TryGetValue(computerId.ToString(), out var computerSoftwareInfo))
    {
        return computerSoftwareInfo;
    }
    
    // Return empty result if no software information found
    return new SoftwareInfo();
}).WithDescription("获取指定计算机的软件信息")
.WithOpenApi(operation => {
    operation.Summary = "获取指定计算机的软件信息";
    operation.Description = "根据计算机ID获取该计算机安装的所有软件信息";

    return operation;
});




//// 添加静态文件支持
app.UseStaticFiles();


app.MapScalarApiReference();

app.Run();


