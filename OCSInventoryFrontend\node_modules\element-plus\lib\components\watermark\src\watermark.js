'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const watermarkProps = runtime.buildProps({
  zIndex: {
    type: Number,
    default: 9
  },
  rotate: {
    type: Number,
    default: -22
  },
  width: Number,
  height: Number,
  image: String,
  content: {
    type: runtime.definePropType([String, Array]),
    default: "Element Plus"
  },
  font: {
    type: runtime.definePropType(Object)
  },
  gap: {
    type: runtime.definePropType(Array),
    default: () => [100, 100]
  },
  offset: {
    type: runtime.definePropType(Array)
  }
});

exports.watermarkProps = watermarkProps;
//# sourceMappingURL=watermark.js.map
