<template>
  <div class="computer-detail-view">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="primary" plain>
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
        <h2 v-if="computerStore.currentComputer">
          {{ computerStore.currentComputer.name || `设备 ${id}` }}
        </h2>
      </div>
      <el-button @click="refreshData" type="primary">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="computerStore.detailLoading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 设备详情内容 -->
    <div v-else-if="computerStore.currentComputer" class="detail-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 硬件信息标签页 -->
        <el-tab-pane label="硬件信息" name="hardware">
          <el-card class="info-card">
            <template #header>
              <span>基本信息</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="设备ID">
                {{ computerStore.currentComputer.id }}
              </el-descriptions-item>
              <el-descriptions-item label="设备名称">
                {{ computerStore.currentComputer.name || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="操作系统">
                {{ computerStore.currentComputer.operatingSystem || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="IP地址">
                {{ computerStore.currentComputer.ipAddress || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="MAC地址">
                {{ computerStore.currentComputer.macAddress || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="最后更新">
                {{ formatDate(computerStore.currentComputer.lastUpdate) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="info-card">
            <template #header>
              <span>硬件配置</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="处理器">
                {{ computerStore.currentComputer.processor || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="内存">
                {{ formatMemory(computerStore.currentComputer.memory) }}
              </el-descriptions-item>
              <el-descriptions-item label="硬盘">
                {{ computerStore.currentComputer.storage || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="主板">
                {{ computerStore.currentComputer.motherboard || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="显卡">
                {{ computerStore.currentComputer.graphics || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="网卡">
                {{ computerStore.currentComputer.networkCard || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-tab-pane>

        <!-- 软件列表标签页 -->
        <el-tab-pane label="已安装软件" name="software">
          <SoftwareList
            :software-list="computerStore.currentComputerSoftware"
            :loading="softwareLoading"
            :total="computerStore.currentComputerSoftware.length"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 错误状态 -->
    <el-alert
      v-else-if="computerStore.error"
      :title="computerStore.error"
      type="error"
      show-icon
      closable
      @close="computerStore.clearError"
    />

    <!-- 无数据状态 -->
    <el-empty v-else description="未找到设备信息" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useComputerStore } from '../stores/computerStore'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'
import SoftwareList from '../components/SoftwareList.vue'

// Props
const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const router = useRouter()
const computerStore = useComputerStore()

// 响应式数据
const activeTab = ref('hardware')
const softwareLoading = ref(false)

// 方法
const goBack = () => {
  router.push('/computers')
}

const refreshData = async () => {
  await Promise.all([
    computerStore.fetchComputerDetails(props.id),
    loadSoftwareData()
  ])
}

const loadSoftwareData = async () => {
  if (activeTab.value === 'software') {
    softwareLoading.value = true
    try {
      await computerStore.fetchComputerSoftware(props.id)
    } finally {
      softwareLoading.value = false
    }
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}

const formatMemory = (memory) => {
  if (!memory) return '-'
  if (typeof memory === 'number') {
    return `${(memory / 1024 / 1024 / 1024).toFixed(2)} GB`
  }
  return memory
}

// 监听标签页切换
const handleTabChange = (tabName) => {
  if (tabName === 'software' && computerStore.currentComputerSoftware.length === 0) {
    loadSoftwareData()
  }
}

// 生命周期
onMounted(async () => {
  await computerStore.fetchComputerDetails(props.id)
})

onUnmounted(() => {
  computerStore.clearCurrentComputer()
})
</script>

<style scoped>
.computer-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.loading-container {
  padding: 20px;
}

.detail-content {
  margin-top: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}
</style>
