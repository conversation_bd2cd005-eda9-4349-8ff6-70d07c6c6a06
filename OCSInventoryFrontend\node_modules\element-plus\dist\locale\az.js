/*! Element Plus v2.10.2 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleAz = factory());
})(this, (function () { 'use strict';

  var az = {
    name: "az",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "T\u0259sdiql\u0259",
        clear: "T\u0259mizl\u0259"
      },
      datepicker: {
        now: "\u0130ndi",
        today: "Bug\xFCn",
        cancel: "\u0130mtina",
        clear: "T\u0259mizl\u0259",
        confirm: "T\u0259sdiql\u0259",
        selectDate: "Tarix se\xE7",
        selectTime: "Saat se\xE7",
        startDate: "Ba\u015Flan\u011F\u0131c Tarixi",
        startTime: "Ba\u015Flan\u011F\u0131c Saat\u0131",
        endDate: "Bitm\u0259 Tarixi",
        endTime: "Bitm\u0259 Saat\u0131",
        prevYear: "\xD6nc\u0259ki il",
        nextYear: "Sonrak\u0131 il",
        prevMonth: "\xD6nc\u0259ki ay",
        nextMonth: "Sonrak\u0131 ay",
        year: "",
        month1: "Yanvar",
        month2: "Fevral",
        month3: "Mart",
        month4: "Aprel",
        month5: "May",
        month6: "\u0130yun",
        month7: "\u0130yul",
        month8: "Avqust",
        month9: "Sentyabr",
        month10: "Oktyabr",
        month11: "Noyabr",
        month12: "Dekabr",
        week: "h\u0259ft\u0259",
        weeks: {
          sun: "Baz",
          mon: "B.e",
          tue: "\xC7.a",
          wed: "\xC7\u0259r",
          thu: "C.a",
          fri: "C\xFCm",
          sat: "\u015E\u0259n"
        },
        months: {
          jan: "Yan",
          feb: "Fev",
          mar: "Mar",
          apr: "Apr",
          may: "May",
          jun: "\u0130yn",
          jul: "\u0130yl",
          aug: "Avq",
          sep: "Sen",
          oct: "Okt",
          nov: "Noy",
          dec: "Dek"
        }
      },
      select: {
        loading: "Y\xFCkl\u0259nir",
        noMatch: "N\u0259tic\u0259 tap\u0131lmad\u0131",
        noData: "M\u0259lumat yoxdur",
        placeholder: "Se\xE7"
      },
      mention: {
        loading: "Y\xFCkl\u0259nir"
      },
      cascader: {
        noMatch: "N\u0259tic\u0259 tap\u0131lmad\u0131",
        loading: "Y\xFCkl\u0259nir",
        placeholder: "Se\xE7",
        noData: "M\u0259lumat yoxdur"
      },
      pagination: {
        goto: "Get",
        pagesize: "/s\u0259hif\u0259",
        total: "Toplam {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "Mesaj",
        confirm: "T\u0259sdiql\u0259",
        cancel: "\u0130mtina",
        error: "S\u0259hv"
      },
      upload: {
        deleteTip: "S\xFCr\xFC\u015Fd\xFCrm\u0259d\u0259n sonra sil",
        delete: "Sil",
        preview: "\xD6n izl\u0259",
        continue: "Davam et"
      },
      table: {
        emptyText: "M\u0259lumat yoxdur",
        confirmFilter: "T\u0259sdiql\u0259",
        resetFilter: "S\u0131f\u0131rla",
        clearFilter: "B\xFCt\xFCn",
        sumText: "C\u0259mi"
      },
      tree: {
        emptyText: "M\u0259lumat yoxdur"
      },
      transfer: {
        noMatch: "N\u0259tic\u0259 tap\u0131lmad\u0131",
        noData: "M\u0259lumat yoxdur",
        titles: ["Siyah\u0131 1", "Siyah\u0131 2"],
        filterPlaceholder: "K\u0259lim\u0259l\u0259ri daxil et",
        noCheckedFormat: "{total} \u0259d\u0259d",
        hasCheckedFormat: "{checked}/{total} se\xE7ildi"
      },
      image: {
        error: "S\u018FHV"
      },
      pageHeader: {
        title: "Geri"
      },
      popconfirm: {
        confirmButtonText: "B\u0259li",
        cancelButtonText: "Xeyr"
      },
      empty: {
        description: "M\u0259lumat yoxdur"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return az;

}));
