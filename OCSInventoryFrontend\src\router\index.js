import { createRouter, createWebHistory } from 'vue-router'
import ComputerListView from '../views/ComputerListView.vue'
import ComputerDetailView from '../views/ComputerDetailView.vue'
import DashboardView from '../views/DashboardView.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: DashboardView,
    redirect: '/computers' // 或者将仪表盘作为首页
  },
  {
    path: '/computers',
    name: 'ComputerList',
    component: ComputerListView
  },
  {
    path: '/computer/:id',
    name: 'ComputerDetail',
    component: ComputerDetailView,
    props: true // 将路由参数作为 props 传入组件
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
