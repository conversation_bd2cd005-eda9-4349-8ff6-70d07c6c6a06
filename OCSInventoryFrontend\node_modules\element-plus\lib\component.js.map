{"version": 3, "file": "component.js", "sources": ["../../../packages/element-plus/component.ts"], "sourcesContent": ["import { ElAffix } from '@element-plus/components/affix'\nimport { El<PERSON>lert } from '@element-plus/components/alert'\nimport { ElAutocomplete } from '@element-plus/components/autocomplete'\nimport { ElAvatar } from '@element-plus/components/avatar'\nimport { ElBacktop } from '@element-plus/components/backtop'\nimport { ElBadge } from '@element-plus/components/badge'\nimport {\n  ElBreadcrumb,\n  ElBreadcrumbItem,\n} from '@element-plus/components/breadcrumb'\nimport { ElButton, ElButtonGroup } from '@element-plus/components/button'\nimport { ElCalendar } from '@element-plus/components/calendar'\nimport { ElCard } from '@element-plus/components/card'\nimport { ElCarousel, ElCarouselItem } from '@element-plus/components/carousel'\nimport { ElCascader } from '@element-plus/components/cascader'\nimport { ElCascaderPanel } from '@element-plus/components/cascader-panel'\nimport { ElCheckTag } from '@element-plus/components/check-tag'\nimport {\n  ElCheckbox,\n  ElCheckboxButton,\n  ElCheckboxGroup,\n} from '@element-plus/components/checkbox'\nimport { ElCol } from '@element-plus/components/col'\nimport { ElCollapse, ElCollapseItem } from '@element-plus/components/collapse'\nimport { ElCollapseTransition } from '@element-plus/components/collapse-transition'\nimport { ElColorPicker } from '@element-plus/components/color-picker'\nimport { ElConfigProvider } from '@element-plus/components/config-provider'\nimport {\n  ElAside,\n  ElContainer,\n  ElFooter,\n  ElHeader,\n  ElMain,\n} from '@element-plus/components/container'\nimport { ElDatePicker } from '@element-plus/components/date-picker'\nimport {\n  ElDescriptions,\n  ElDescriptionsItem,\n} from '@element-plus/components/descriptions'\nimport { ElDialog } from '@element-plus/components/dialog'\nimport { ElDivider } from '@element-plus/components/divider'\nimport { ElDrawer } from '@element-plus/components/drawer'\nimport {\n  ElDropdown,\n  ElDropdownItem,\n  ElDropdownMenu,\n} from '@element-plus/components/dropdown'\nimport { ElEmpty } from '@element-plus/components/empty'\nimport { ElForm, ElFormItem } from '@element-plus/components/form'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ElImage } from '@element-plus/components/image'\nimport { ElImageViewer } from '@element-plus/components/image-viewer'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElInputNumber } from '@element-plus/components/input-number'\nimport { ElInputTag } from '@element-plus/components/input-tag'\nimport { ElLink } from '@element-plus/components/link'\nimport {\n  ElMenu,\n  ElMenuItem,\n  ElMenuItemGroup,\n  ElSubMenu,\n} from '@element-plus/components/menu'\nimport { ElPageHeader } from '@element-plus/components/page-header'\nimport { ElPagination } from '@element-plus/components/pagination'\nimport { ElPopconfirm } from '@element-plus/components/popconfirm'\nimport { ElPopover } from '@element-plus/components/popover'\nimport { ElPopper } from '@element-plus/components/popper'\nimport { ElProgress } from '@element-plus/components/progress'\nimport {\n  ElRadio,\n  ElRadioButton,\n  ElRadioGroup,\n} from '@element-plus/components/radio'\nimport { ElRate } from '@element-plus/components/rate'\nimport { ElResult } from '@element-plus/components/result'\nimport { ElRow } from '@element-plus/components/row'\nimport { ElScrollbar } from '@element-plus/components/scrollbar'\nimport {\n  ElOption,\n  ElOptionGroup,\n  ElSelect,\n} from '@element-plus/components/select'\nimport { ElSelectV2 } from '@element-plus/components/select-v2'\nimport { ElSkeleton, ElSkeletonItem } from '@element-plus/components/skeleton'\nimport { ElSlider } from '@element-plus/components/slider'\nimport { ElSpace } from '@element-plus/components/space'\nimport { ElStatistic } from '@element-plus/components/statistic'\nimport { ElCountdown } from '@element-plus/components/countdown'\nimport { ElStep, ElSteps } from '@element-plus/components/steps'\nimport { ElSwitch } from '@element-plus/components/switch'\nimport { ElTable, ElTableColumn } from '@element-plus/components/table'\nimport { ElAutoResizer, ElTableV2 } from '@element-plus/components/table-v2'\nimport { ElTabPane, ElTabs } from '@element-plus/components/tabs'\nimport { ElTag } from '@element-plus/components/tag'\nimport { ElText } from '@element-plus/components/text'\nimport { ElTimePicker } from '@element-plus/components/time-picker'\nimport { ElTimeSelect } from '@element-plus/components/time-select'\nimport { ElTimeline, ElTimelineItem } from '@element-plus/components/timeline'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElTooltipV2 } from '@element-plus/components/tooltip-v2'\nimport { ElTransfer } from '@element-plus/components/transfer'\nimport { ElTree } from '@element-plus/components/tree'\nimport { ElTreeSelect } from '@element-plus/components/tree-select'\nimport { ElTreeV2 } from '@element-plus/components/tree-v2'\nimport { ElUpload } from '@element-plus/components/upload'\nimport { ElWatermark } from '@element-plus/components/watermark'\nimport { ElTour, ElTourStep } from '@element-plus/components/tour'\nimport { ElAnchor, ElAnchorLink } from '@element-plus/components/anchor'\nimport { ElSegmented } from '@element-plus/components/segmented'\nimport { ElMention } from '@element-plus/components/mention'\nimport { ElSplitter, ElSplitterPanel } from '@element-plus/components/splitter'\n\nimport type { Plugin } from 'vue'\n\nexport default [\n  ElAffix,\n  ElAlert,\n  ElAutocomplete,\n  ElAutoResizer,\n  ElAvatar,\n  ElBacktop,\n  ElBadge,\n  ElBreadcrumb,\n  ElBreadcrumbItem,\n  ElButton,\n  ElButtonGroup,\n  ElCalendar,\n  ElCard,\n  ElCarousel,\n  ElCarouselItem,\n  ElCascader,\n  ElCascaderPanel,\n  ElCheckTag,\n  ElCheckbox,\n  ElCheckboxButton,\n  ElCheckboxGroup,\n  ElCol,\n  ElCollapse,\n  ElCollapseItem,\n  ElCollapseTransition,\n  ElColorPicker,\n  ElConfigProvider,\n  ElContainer,\n  ElAside,\n  ElFooter,\n  ElHeader,\n  ElMain,\n  ElDatePicker,\n  ElDescriptions,\n  ElDescriptionsItem,\n  ElDialog,\n  ElDivider,\n  ElDrawer,\n  ElDropdown,\n  ElDropdownItem,\n  ElDropdownMenu,\n  ElEmpty,\n  ElForm,\n  ElFormItem,\n  ElIcon,\n  ElImage,\n  ElImageViewer,\n  ElInput,\n  ElInputNumber,\n  ElInputTag,\n  ElLink,\n  ElMenu,\n  ElMenuItem,\n  ElMenuItemGroup,\n  ElSubMenu,\n  ElPageHeader,\n  ElPagination,\n  ElPopconfirm,\n  ElPopover,\n  ElPopper,\n  ElProgress,\n  ElRadio,\n  ElRadioButton,\n  ElRadioGroup,\n  ElRate,\n  ElResult,\n  ElRow,\n  ElScrollbar,\n  ElSelect,\n  ElOption,\n  ElOptionGroup,\n  ElSelectV2,\n  ElSkeleton,\n  ElSkeletonItem,\n  ElSlider,\n  ElSpace,\n  ElStatistic,\n  ElCountdown,\n  ElSteps,\n  ElStep,\n  ElSwitch,\n  ElTable,\n  ElTableColumn,\n  ElTableV2,\n  ElTabs,\n  ElTabPane,\n  ElTag,\n  ElText,\n  ElTimePicker,\n  ElTimeSelect,\n  ElTimeline,\n  ElTimelineItem,\n  ElTooltip,\n  ElTooltipV2,\n  ElTransfer,\n  ElTree,\n  ElTreeSelect,\n  ElTreeV2,\n  ElUpload,\n  ElWatermark,\n  ElTour,\n  ElTourStep,\n  ElAnchor,\n  ElAnchorLink,\n  ElSegmented,\n  ElMention,\n  ElSplitter,\n  ElSplitterPanel,\n] as Plugin[]\n"], "names": ["ElAffix", "<PERSON><PERSON><PERSON><PERSON>", "ElAutocomplete", "ElAutoResizer", "El<PERSON><PERSON><PERSON>", "ElBacktop", "ElBadge", "ElBreadcrumb", "ElBreadcrumbItem", "ElButton", "ElButtonGroup", "ElCalendar", "ElCard", "ElCarousel", "ElCarouselItem", "ElCascader", "ElCascaderPanel", "ElCheckTag", "ElCheckbox", "ElCheckboxButton", "ElCheckboxGroup", "ElCol", "ElCollapse", "ElCollapseItem", "ElCollapseTransition", "ElColorPicker", "ElConfigProvider", "ElC<PERSON><PERSON>", "ElAside", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ElDatePicker", "ElDescriptions", "ElDescriptionsItem", "ElDialog", "ElDivider", "<PERSON><PERSON><PERSON><PERSON>", "ElDropdown", "ElDropdownItem", "ElDropdownMenu", "ElEmpty", "ElForm", "ElFormItem", "ElIcon", "ElImage", "ElImageViewer", "ElInput", "ElInputNumber", "ElInputTag", "ElLink", "ElMenu", "ElMenuItem", "ElMenuItemGroup", "ElSubMenu", "ElPageHeader", "ElPagination", "ElPopconfirm", "ElPopover", "ElPopper", "ElProgress", "ElRadio", "ElRadioButton", "ElRadioGroup", "ElRate", "ElResult", "ElRow", "ElScrollbar", "ElSelect", "ElOption", "ElOptionGroup", "ElSelectV2", "ElSkeleton", "ElSkeletonItem", "ElSlider", "ElSpace", "ElStatistic", "ElCountdown", "ElSteps", "ElStep", "ElSwitch", "ElTable", "ElTableColumn", "ElTableV2", "ElTabs", "ElTabPane", "ElTag", "ElText", "ElTimePicker", "ElTimeSelect", "ElTimeline", "ElTimelineItem", "ElTooltip", "ElTooltipV2", "ElTransfer", "ElTree", "ElTreeSelect", "ElTreeV2", "ElUpload", "ElWatermark", "ElTour", "ElTourStep", "ElAnchor", "ElAnchorLink", "ElSegmented", "ElMention", "ElSplitter", "ElSplitterPanel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA,iBAAe;AACf,EAAEA,aAAO;AACT,EAAEC,eAAO;AACT,EAAEC,sBAAc;AAChB,EAAEC,qBAAa;AACf,EAAEC,gBAAQ;AACV,EAAEC,iBAAS;AACX,EAAEC,eAAO;AACT,EAAEC,oBAAY;AACd,EAAEC,wBAAgB;AAClB,EAAEC,gBAAQ;AACV,EAAEC,qBAAa;AACf,EAAEC,kBAAU;AACZ,EAAEC,cAAM;AACR,EAAEC,kBAAU;AACZ,EAAEC,sBAAc;AAChB,EAAEC,kBAAU;AACZ,EAAEC,uBAAe;AACjB,EAAEC,kBAAU;AACZ,EAAEC,kBAAU;AACZ,EAAEC,wBAAgB;AAClB,EAAEC,uBAAe;AACjB,EAAEC,aAAK;AACP,EAAEC,kBAAU;AACZ,EAAEC,sBAAc;AAChB,EAAEC,4BAAoB;AACtB,EAAEC,qBAAa;AACf,EAAEC,wBAAgB;AAClB,EAAEC,mBAAW;AACb,EAAEC,eAAO;AACT,EAAEC,gBAAQ;AACV,EAAEC,gBAAQ;AACV,EAAEC,cAAM;AACR,EAAEC,oBAAY;AACd,EAAEC,sBAAc;AAChB,EAAEC,0BAAkB;AACpB,EAAEC,gBAAQ;AACV,EAAEC,iBAAS;AACX,EAAEC,gBAAQ;AACV,EAAEC,kBAAU;AACZ,EAAEC,sBAAc;AAChB,EAAEC,sBAAc;AAChB,EAAEC,eAAO;AACT,EAAEC,cAAM;AACR,EAAEC,kBAAU;AACZ,EAAEC,cAAM;AACR,EAAEC,eAAO;AACT,EAAEC,qBAAa;AACf,EAAEC,eAAO;AACT,EAAEC,qBAAa;AACf,EAAEC,kBAAU;AACZ,EAAEC,cAAM;AACR,EAAEC,cAAM;AACR,EAAEC,kBAAU;AACZ,EAAEC,uBAAe;AACjB,EAAEC,iBAAS;AACX,EAAEC,oBAAY;AACd,EAAEC,oBAAY;AACd,EAAEC,oBAAY;AACd,EAAEC,iBAAS;AACX,EAAEC,gBAAQ;AACV,EAAEC,kBAAU;AACZ,EAAEC,eAAO;AACT,EAAEC,qBAAa;AACf,EAAEC,oBAAY;AACd,EAAEC,cAAM;AACR,EAAEC,gBAAQ;AACV,EAAEC,aAAK;AACP,EAAEC,mBAAW;AACb,EAAEC,gBAAQ;AACV,EAAEC,gBAAQ;AACV,EAAEC,qBAAa;AACf,EAAEC,kBAAU;AACZ,EAAEC,kBAAU;AACZ,EAAEC,sBAAc;AAChB,EAAEC,gBAAQ;AACV,EAAEC,eAAO;AACT,EAAEC,mBAAW;AACb,EAAEC,mBAAW;AACb,EAAEC,eAAO;AACT,EAAEC,cAAM;AACR,EAAEC,gBAAQ;AACV,EAAEC,eAAO;AACT,EAAEC,qBAAa;AACf,EAAEC,iBAAS;AACX,EAAEC,cAAM;AACR,EAAEC,iBAAS;AACX,EAAEC,aAAK;AACP,EAAEC,cAAM;AACR,EAAEC,oBAAY;AACd,EAAEC,oBAAY;AACd,EAAEC,mBAAU;AACZ,EAAEC,uBAAc;AAChB,EAAEC,kBAAS;AACX,EAAEC,oBAAW;AACb,EAAEC,mBAAU;AACZ,EAAEC,eAAM;AACR,EAAEC,qBAAY;AACd,EAAEC,iBAAQ;AACV,EAAEC,iBAAQ;AACV,EAAEC,oBAAW;AACb,EAAEC,eAAM;AACR,EAAEC,mBAAU;AACZ,EAAEC,iBAAQ;AACV,EAAEC,qBAAY;AACd,EAAEC,oBAAW;AACb,EAAEC,kBAAS;AACX,EAAEC,mBAAU;AACZ,EAAEC,wBAAe;AACjB,CAAC;;;;"}