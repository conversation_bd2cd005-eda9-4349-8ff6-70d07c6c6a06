﻿using System.Text.Json.Serialization;

namespace OCSInventoryDotnetServer.Entity
{
    /// <summary>
    /// 软件信息
    /// </summary>
    public class SoftwareInfo
    {
        /// <summary>
        /// 硬件信息
        /// </summary>
        [JsonPropertyName("hardware")]
        public Hardware? Hardware { get; set; }

        /// <summary>
        /// 软件列表
        /// </summary>
        [JsonPropertyName("software")]
        public List<SoftwareDetail> Software { get; set; } = new();
    }

    /// <summary>
    /// 软件详情
    /// </summary>
    public class SoftwareDetail
    {
        /// <summary>
        /// 位宽
        /// </summary>
        [JsonPropertyName("BITSWIDTH")]
        public int Bitswidth { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("COMMENTS")]
        public string? Comments { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [JsonPropertyName("FILENAME")]
        public string? Filename { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        [JsonPropertyName("FILESIZE")]
        public int Filesize { get; set; }

        /// <summary>
        /// 文件夹
        /// </summary>
        [JsonPropertyName("FOLDER")]
        public string? Folder { get; set; }

        /// <summary>
        /// GUID
        /// </summary>
        [JsonPropertyName("GUID")]
        public string? Guid { get; set; }

        /// <summary>
        /// 硬件ID
        /// </summary>
        [JsonPropertyName("HARDWARE_ID")]
        public int HardwareId { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        [JsonPropertyName("ID")]
        public int Id { get; set; }

        /// <summary>
        /// 安装日期
        /// </summary>
        [JsonPropertyName("INSTALLDATE")]
        public string? InstallDate { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        [JsonPropertyName("LANGUAGE")]
        public string? Language { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [JsonPropertyName("NAME")]
        public string? Name { get; set; }

        /// <summary>
        /// 发布者
        /// </summary>
        [JsonPropertyName("PUBLISHER")]
        public string? Publisher { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        [JsonPropertyName("SOURCE")]
        public int Source { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [JsonPropertyName("VERSION")]
        public string? Version { get; set; }
    }
}
