import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api', // 使用代理，实际指向 http://localhost:5168
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    console.log('API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log('API Response:', response.status, response.config.url)
    return response
  },
  error => {
    console.error('API Error:', error.response?.status, error.config?.url, error.message)
    return Promise.reject(error)
  }
)

export default {
  // 获取所有设备ID列表
  getDevices() {
    return apiClient.get('/devices')
  },

  // 分页获取设备详细信息
  getComputers(start = 0, limit = 20) {
    return apiClient.get(`/ListComputersDetails?start=${start}&limit=${limit}`)
  },

  // 获取单个设备详细信息
  getComputerDetails(id) {
    return apiClient.get(`/ListOneComputerDetails?id=${id}`)
  },

  // 获取设备软件信息
  getComputerSoftware(computerId) {
    return apiClient.get(`/Softwares?computerId=${computerId}`)
  },

  // 获取最近更新的设备
  getLastUpdatedComputers(timestamp) {
    return apiClient.get(`/ListLastUpdatedComputers?timestamp=${timestamp}`)
  }
}
