<template>
  <div class="software-list">
    <el-table
      :data="softwareList"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      empty-text="暂无软件数据"
    >
      <el-table-column
        prop="name"
        label="软件名称"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        prop="version"
        label="版本"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="publisher"
        label="发行商"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="installDate"
        label="安装日期"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="size"
        label="大小"
        min-width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-if="row.size">{{ formatSize(row.size) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      v-if="total > 0"
      :total="total"
      :page="currentPage"
      :size="pageSize"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Pagination from './Pagination.vue'

// Props
const props = defineProps({
  softwareList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 20
  }
})

// Emits
const emit = defineEmits(['page-change', 'size-change'])

// 格式化文件大小
const formatSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 事件处理
const handlePageChange = (page) => {
  emit('page-change', page)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}
</script>

<style scoped>
.software-list {
  width: 100%;
}
</style>
