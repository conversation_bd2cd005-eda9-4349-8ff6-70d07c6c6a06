﻿namespace OCSInventoryDotnetServer.Entity
{
    public class ComputerDetailsResponse : Dictionary<string, ComputerDetails>
    {
    }

    public class ComputerDetails
    {
        public List<AccountInfo> AccountInfo { get; set; } = new();
        public List<Battery> Batteries { get; set; } = new();
        public List<Bios> Bios { get; set; } = new();
        public List<Controller> Controllers { get; set; } = new();
        public List<Cpu> Cpus { get; set; } = new();
        public List<Device> Devices { get; set; } = new();
        public List<DownloadHistory> Download_History { get; set; } = new();
        public List<Drive> Drives { get; set; } = new();
        public List<GroupsCache> Groups_Cache { get; set; } = new();
        public Hardware Hardware { get; set; } = new();
        public List<Input> Inputs { get; set; } = new();
        public List<ItMgmtComment> Itmgmt_Comments { get; set; } = new();
        public List<JavaInfo> Javainfo { get; set; } = new();
        public List<JournalLog> Journallog { get; set; } = new();
        public List<LocalGroup> Local_Groups { get; set; } = new();
        public List<LocalUser> Local_Users { get; set; } = new();
        public List<Memory> Memories { get; set; } = new();
        public List<Modem> Modems { get; set; } = new();
        public List<Monitor> Monitors { get; set; } = new();
        public List<Network> Networks { get; set; } = new();
        public List<Port> Ports { get; set; } = new();
        public List<Printer> Printers { get; set; } = new();
        public List<Registry> Registry { get; set; } = new();
        public List<Repository> Repository { get; set; } = new();
        public List<Sim> Sim { get; set; } = new();
        public List<Slot> Slots { get; set; } = new();
        public List<Software> Software { get; set; } = new();
        public List<Sound> Sounds { get; set; } = new();
        public List<Storage> Storages { get; set; } = new();
        public List<UsbDevice> Usbdevices { get; set; } = new();
        public List<Video> Videos { get; set; } = new();
        public List<VirtualMachine> Virtualmachines { get; set; } = new();
    }

    public class AccountInfo
    {
        public int HARDWARE_ID { get; set; }
        public string TAG { get; set; } = "";
    }

    public class Battery
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string CHEMISTRY { get; set; } = "";
        public string VOLTAGE { get; set; } = "";
        public string CAPACITY { get; set; } = "";
    }

    public class Bios
    {
        public string ASSETTAG { get; set; } = "";
        public string BDATE { get; set; } = "";
        public string BMANUFACTURER { get; set; } = "";
        public string BVERSION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public string MMANUFACTURER { get; set; } = "";
        public string MMODEL { get; set; } = "";
        public string MSN { get; set; } = "";
        public string SMANUFACTURER { get; set; } = "";
        public string SMODEL { get; set; } = "";
        public string SSN { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class Controller
    {
        public string CAPTION { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string MANUFACTURER { get; set; } = "";
        public string NAME { get; set; } = "";
        public string TYPE { get; set; } = "";
        public string VERSION { get; set; } = "";
    }

    public class Cpu
    {
        public int CORES { get; set; }
        public string CPUARCH { get; set; } = "";
        public int CURRENT_ADDRESS_WIDTH { get; set; }
        public string CURRENT_SPEED { get; set; } = "";
        public int DATA_WIDTH { get; set; }
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string L2CACHESIZE { get; set; } = "";
        public int LOGICAL_CPUS { get; set; }
        public string MANUFACTURER { get; set; } = "";
        public string SERIALNUMBER { get; set; } = "";
        public string SOCKET { get; set; } = "";
        public string SPEED { get; set; } = "";
        public string TYPE { get; set; } = "";
        public string VOLTAGE { get; set; } = "";
    }

    public class Device
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class DownloadHistory
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string PKG_ID { get; set; } = "";
        public string PKG_NAME { get; set; } = "";
    }

    public class Drive
    {
        public string CREATEDATE { get; set; } = "";
        public string FILESYSTEM { get; set; } = "";
        public int FREE { get; set; }
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string LETTER { get; set; } = "";
        public int NUMFILES { get; set; }
        public int TOTAL { get; set; }
        public string TYPE { get; set; } = "";
        public string VOLUMN { get; set; } = "";
    }

    public class GroupsCache
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string CACHE_ID { get; set; } = "";
    }

    public class Hardware
    {
        public string ARCH { get; set; } = "";
        public string? ARCHIVE { get; set; }
        public string? CATEGORY_ID { get; set; }
        public int CHECKSUM { get; set; }
        public string DEFAULTGATEWAY { get; set; } = "";
        public string? DESCRIPTION { get; set; }
        public string DEVICEID { get; set; } = "";
        public string? DNS { get; set; }
        public string? ETIME { get; set; }
        public int FIDELITY { get; set; }
        public int ID { get; set; }
        public string IPADDR { get; set; } = "";
        public string IPSRC { get; set; } = "";
        public string LASTCOME { get; set; } = "";
        public string LASTDATE { get; set; } = "";
        public int MEMORY { get; set; }
        public string NAME { get; set; } = "";
        public string OSCOMMENTS { get; set; } = "";
        public string OSNAME { get; set; } = "";
        public string OSVERSION { get; set; } = "";
        public int PROCESSORN { get; set; }
        public int PROCESSORS { get; set; }
        public string PROCESSORT { get; set; } = "";
        public string? QUALITY { get; set; }
        public int SSTATE { get; set; }
        public int SWAP { get; set; }
        public int TYPE { get; set; }
        public string USERAGENT { get; set; } = "";
        public string USERDOMAIN { get; set; } = "";
        public string USERID { get; set; } = "";
        public string UUID { get; set; } = "";
        public string? WINCOMPANY { get; set; }
        public string WINOWNER { get; set; } = "";
        public string WINPRODID { get; set; } = "";
        public string WINPRODKEY { get; set; } = "";
        public string WORKGROUP { get; set; } = "";
    }

    public class Input
    {
        public string CAPTION { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string INTERFACE { get; set; } = "";
        public string MANUFACTURER { get; set; } = "";
        public string POINTTYPE { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class ItMgmtComment
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string COMMENTS { get; set; } = "";
    }

    public class JavaInfo
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string VERSION { get; set; } = "";
    }

    public class JournalLog
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string LOGNAME { get; set; } = "";
    }

    public class LocalGroup
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
    }

    public class LocalUser
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
    }

    public class Memory
    {
        public int CAPACITY { get; set; }
        public string CAPTION { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public int NUMSLOTS { get; set; }
        public string PURPOSE { get; set; } = "";
        public string SERIALNUMBER { get; set; } = "";
        public string SPEED { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class Modem
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class Monitor
    {
        public string CAPTION { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string MANUFACTURER { get; set; } = "";
        public string SERIAL { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class Network
    {
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string IPADDRESS { get; set; } = "";
        public string IPDHCP { get; set; } = "";
        public string IPGATEWAY { get; set; } = "";
        public string IPMASK { get; set; } = "";
        public string IPSUBNET { get; set; } = "";
        public string MACADDR { get; set; } = "";
        public string MTU { get; set; } = "";
        public string SPEED { get; set; } = "";
        public string STATUS { get; set; } = "";
        public string TYPE { get; set; } = "";
        public string TYPEMIB { get; set; } = "";
        public int VIRTUALDEV { get; set; }
    }

    public class Port
    {
        public string CAPTION { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class Printer
    {
        public string COMMENT { get; set; } = "";
        public string DESCRIPTION { get; set; } = "";
        public string DRIVER { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public int NETWORK { get; set; }
        public string PORT { get; set; } = "";
        public string RESOLUTION { get; set; } = "";
        public string SERVERNAME { get; set; } = "";
        public int SHARED { get; set; }
        public string SHARENAME { get; set; } = "";
    }

    public class Registry
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string REGVALUE { get; set; } = "";
    }

    public class Repository
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string TAG { get; set; } = "";
    }

    public class Sim
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string OPERATOR { get; set; } = "";
    }

    public class Slot
    {
        public string DESCRIPTION { get; set; } = "";
        public string DESIGNATION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public int PSHARE { get; set; }
        public string PURPOSE { get; set; } = "";
        public string STATUS { get; set; } = "";
    }

    public class Software
    {
        public string ARCHITECTURE { get; set; } = "";
        public int BITSWIDTH { get; set; }
        public string COMMENTS { get; set; } = "";
        public string FILENAME { get; set; } = "";
        public int FILESIZE { get; set; }
        public string FOLDER { get; set; } = "";
        public string GUID { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string INSTALLDATE { get; set; } = "";
        public string LANGUAGE { get; set; } = "";
        public int NAME_ID { get; set; }
        public int PUBLISHER_ID { get; set; }
        public int SOURCE { get; set; }
        public int VERSION_ID { get; set; }
    }

    public class Sound
    {
        public string DESCRIPTION { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string MANUFACTURER { get; set; } = "";
        public string NAME { get; set; } = "";
    }

    public class Storage
    {
        public string DESCRIPTION { get; set; } = "";
        public int DISKSIZE { get; set; }
        public string FIRMWARE { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string MANUFACTURER { get; set; } = "";
        public string MODEL { get; set; } = "";
        public string NAME { get; set; } = "";
        public string SERIALNUMBER { get; set; } = "";
        public string TYPE { get; set; } = "";
    }

    public class UsbDevice
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string VENDORID { get; set; } = "";
        public string PRODUCTID { get; set; } = "";
    }

    public class Video
    {
        public string CHIPSET { get; set; } = "";
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string MEMORY { get; set; } = "";
        public string NAME { get; set; } = "";
        public string RESOLUTION { get; set; } = "";
    }

    public class VirtualMachine
    {
        public int HARDWARE_ID { get; set; }
        public int ID { get; set; }
        public string NAME { get; set; } = "";
        public string TYPE { get; set; } = "";
    }
}