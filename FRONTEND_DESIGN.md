# OCS Inventory 前端设计文档

## 1. 项目概述

本项目是 OCS Inventory 服务端的 Web 前端界面，旨在为用户提供一个直观、易用的界面来查看和管理 OCS Inventory 中的设备信息。用户可以通过该界面查看设备列表、设备详细信息、已安装软件等。

## 2. 技术栈

*   **框架**: Vue 3 (Composition API)
*   **路由**: Vue Router
*   **状态管理**: Pinia
*   **UI 库**: Element Plus (或 Vuetify / Ant Design Vue)
*   **HTTP 请求**: Axios
*   **构建工具**: Vite

## 3. 功能模块设计

根据后端 API，前端可以划分为以下几个主要功能模块：

### 3.1. 设备列表页

*   **功能描述**: 以分页列表的形式展示所有计算机设备。提供搜索和筛选功能。
*   **对应后端 API**:
    *   `GET /devices`: 获取所有设备 ID 列表 (可用于快速概览)。
    *   `GET /ListComputersDetails?start={start}&limit={limit}`: 分页获取设备详细信息。
*   **页面路由**: `/computers`
*   **页面组件**: `views/ComputerListView.vue`

### 3.2. 设备详情页

*   **功能描述**: 展示单个设备的详细信息，包括硬件信息、网络配置和已安装软件列表。
*   **对应后端 API**:
    *   `GET /ListOneComputerDetails?id={id}`: 获取计算机硬件、网络等详细信息。
    *   `GET /Softwares?computerId={id}`: 获取计算机的软件信息。
*   **页面路由**: `/computer/:id`
*   **页面组件**: `views/ComputerDetailView.vue`

### 3.3. 仪表盘/首页 (可选)

*   **功能描述**: 展示一些汇总信息和快速入口。例如：最近更新的设备列表、设备总数统计等。
*   **对应后端 API**:
    *   `GET /ListLastUpdatedComputers?timestamp={timestamp}`: 获取最近更新的设备。
*   **页面路由**: `/` 或 `/dashboard`
*   **页面组件**: `views/DashboardView.vue`

## 4. 组件设计

### 4.1. 页面视图组件

*   `views/ComputerListView.vue`:
    *   负责获取和展示设备分页列表。
    *   包含分页组件 `components/Pagination.vue`。
    *   列表中的每一项都可以点击，通过 `router-link` 跳转到设备详情页。
*   `views/ComputerDetailView.vue`:
    *   根据路由参数中的 `id` 获取并展示设备详情。
    *   详情内容可以分 Tab 页展示，如 "硬件信息"、"软件列表"。
    *   "软件列表" Tab 中可以包含 `components/SoftwareList.vue` 组件。
*   `views/DashboardView.vue`:
    *   展示统计数据和图表。

### 4.2. 通用组件

*   `components/Pagination.vue`: 可复用的分页组件。
*   `components/SoftwareList.vue`: 展示软件列表的组件。
*   `layouts/MainLayout.vue`: 主布局文件，包含页头、侧边栏导航和内容区域。

## 5. 路由设计 (Vue Router)

```javascript
import { createRouter, createWebHistory } from 'vue-router';
import ComputerListView from '../views/ComputerListView.vue';
import ComputerDetailView from '../views/ComputerDetailView.vue';
import DashboardView from '../views/DashboardView.vue';

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: DashboardView,
    redirect: '/computers' // 或者将仪表盘作为首页
  },
  {
    path: '/computers',
    name: 'ComputerList',
    component: ComputerListView
  },
  {
    path: '/computer/:id',
    name: 'ComputerDetail',
    component: ComputerDetailView,
    props: true // 将路由参数作为 props 传入组件
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
```

## 6. 状态管理 (Pinia)

创建 `stores` 目录来管理应用的状态。

*   **`stores/computerStore.js`**:
    *   `state`:
        *   `computers`: 存储设备列表。
        *   `totalComputers`: 设备总数。
        *   `currentComputer`: 当前查看的设备详情。
    *   `actions`:
        *   `fetchComputers(start, limit)`: 调用 API 获取设备列表。
        *   `fetchComputerDetails(id)`: 调用 API 获取单个设备详情。
        *   `fetchComputerSoftware(id)`: 调用 API 获取设备软件列表。

## 7. API 服务层

建议创建一个服务层来封装所有对后端 API 的调用。

*   **`services/api.js` (使用 Axios)**:

```javascript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:5168', // 你的后端地址
  headers: {
    'Content-Type': 'application/json'
  }
});

export default {
  getComputers(start, limit) {
    return apiClient.get(`/ListComputersDetails?start=${start}&limit=${limit}`);
  },
  getComputerDetails(id) {
    return apiClient.get(`/ListOneComputerDetails?id=${id}`);
  },
  getComputerSoftware(computerId) {
    return apiClient.get(`/Softwares?computerId=${computerId}`);
  }
  // ... 其他 API 调用
};
```

## 8. UI 界面简稿

### 8.1. 设备列表页

*   **顶部**: 搜索框。
*   **中部**: 一个表格 (Table)，列包含：`ID`, `名称`, `操作系统`, `IP 地址`, `最后更新时间` 等。
*   **底部**: 分页组件。

### 8.2. 设备详情页

*   **顶部**: 设备名称和主要标识。
*   **中部**: 使用标签页 (Tabs) 分隔不同类别的信息。
    *   **硬件标签页**: 使用描述列表 (Descriptions) 展示 CPU、内存、磁盘、主板等信息。
    *   **软件标签页**: 一个表格，展示已安装软件的 `名称`, `版本`, `发行商`。

## 9. 开发步骤建议

1.  使用 Vite 初始化 Vue 3 项目。
2.  安装 `vue-router`, `pinia`, `axios`, 和一个 UI 库 (如 `element-plus`)。
3.  配置 `main.js` 引入并使用上述插件。
4.  创建 `services/api.js` 并配置基础 `baseURL`。
5.  创建 Pinia store (`stores/computerStore.js`)。
6.  创建路由 (`router/index.js`) 和对应的视图组件 (`views/*.vue`)。
7.  从 `ComputerListView.vue` 开始，实现设备列表的获取和展示。
8.  实现分页功能。
9.  实现从列表页到详情页的跳转。
10. 在 `ComputerDetailView.vue` 中获取并展示设备详细信息和软件列表。
11. 完善 UI/UX。
