'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var et = {
  name: "et",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "T\xFChjenda"
    },
    datepicker: {
      now: "<PERSON>rae<PERSON>",
      today: "T\xE4na",
      cancel: "T\xFChista",
      clear: "T\xFChjenda",
      confirm: "OK",
      selectDate: "Vali kuup\xE4ev",
      selectTime: "Vali kellaaeg",
      startDate: "Alguskuup\xE4ev",
      startTime: "Algusaeg",
      endDate: "L\xF5pukuup\xE4ev",
      endTime: "L\xF5puaeg",
      prevYear: "Eelmine aasta",
      nextYear: "J\xE4rgmine aasta",
      prevMonth: "Eelmine kuu",
      nextMonth: "J\xE4rgmine kuu",
      year: "",
      month1: "<PERSON><PERSON>ua<PERSON>",
      month2: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      month3: "M\xE4rts",
      month4: "Aprill",
      month5: "Mai",
      month6: "<PERSON><PERSON>",
      month7: "<PERSON>uli",
      month8: "August",
      month9: "September",
      month10: "Oktoober",
      month11: "November",
      month12: "Detsember",
      weeks: {
        sun: "P",
        mon: "E",
        tue: "T",
        wed: "K",
        thu: "N",
        fri: "R",
        sat: "L"
      },
      months: {
        jan: "Jaan",
        feb: "Veeb",
        mar: "M\xE4r",
        apr: "Apr",
        may: "Mai",
        jun: "Juun",
        jul: "Juul",
        aug: "Aug",
        sep: "Sept",
        oct: "Okt",
        nov: "Nov",
        dec: "Dets"
      }
    },
    select: {
      loading: "Laadimine",
      noMatch: "Sobivad andmed puuduvad",
      noData: "Andmed puuduvad",
      placeholder: "Vali"
    },
    mention: {
      loading: "Laadimine"
    },
    cascader: {
      noMatch: "Sobivad andmed puuduvad",
      loading: "Laadimine",
      placeholder: "Vali",
      noData: "Andmed puuduvad"
    },
    pagination: {
      goto: "Mine lehele",
      pagesize: "/page",
      total: "Kokku {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Teade",
      confirm: "OK",
      cancel: "T\xFChista",
      error: "Vigane sisend"
    },
    upload: {
      deleteTip: 'Vajuta "Kustuta", et eemaldada',
      delete: "Kustuta",
      preview: "Eelvaate",
      continue: "J\xE4tka"
    },
    table: {
      emptyText: "Andmed puuduvad",
      confirmFilter: "Kinnita",
      resetFilter: "Taasta",
      clearFilter: "K\xF5ik",
      sumText: "Summa"
    },
    tree: {
      emptyText: "Andmed puuduvad"
    },
    transfer: {
      noMatch: "Sobivad andmed puuduvad",
      noData: "Andmed puuduvad",
      titles: ["Loend 1", "Loend 2"],
      filterPlaceholder: "Sisesta m\xE4rks\xF5na",
      noCheckedFormat: "{total} objekti",
      hasCheckedFormat: "{checked}/{total} valitud"
    },
    image: {
      error: "Eba\xF5nnestus"
    },
    pageHeader: {
      title: "Tagasi"
    },
    popconfirm: {
      confirmButtonText: "Jah",
      cancelButtonText: "Ei"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = et;
//# sourceMappingURL=et.js.map
