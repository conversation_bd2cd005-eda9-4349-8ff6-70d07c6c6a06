[{"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__0", "RelativePath": "devices", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Description": "获取设备id'"}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__4", "RelativePath": "ListComputersDetails", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "start", "Type": "System.Int32", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "OCSInventoryDotnetServer.Entity.ComputerDetailsResponse", "MediaTypes": ["application/json"], "StatusCode": 200}], "Description": "获取指定范围内的计算机详细信息"}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__6", "RelativePath": "ListLastUpdatedComputers", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "timestamp", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "OCSInventoryDotnetServer.Entity.DeviceId", "MediaTypes": ["application/json"], "StatusCode": 200}], "Description": "列出最后更新的计算机id"}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__2", "RelativePath": "ListOneComputerDetails", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "OCSInventoryDotnetServer.Entity.ComputerDetailsResponse", "MediaTypes": ["application/json"], "StatusCode": 200}], "Description": "根据id获取计算机详细信息"}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__8", "RelativePath": "Softwares", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "computerId", "Type": "System.Int32", "IsRequired": true}, {"Name": "start", "Type": "System.Int32", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": true}, {"Name": "soft", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "OCSInventoryDotnetServer.Entity.SoftwareInfo", "MediaTypes": ["application/json"], "StatusCode": 200}], "Description": "获取指定计算机的软件信息"}]