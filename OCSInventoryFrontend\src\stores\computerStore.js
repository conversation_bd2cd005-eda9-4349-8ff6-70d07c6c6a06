import { defineStore } from 'pinia'
import api from '../services/api'

export const useComputerStore = defineStore('computer', {
  state: () => ({
    // 设备列表相关
    computers: [],
    totalComputers: 0,
    currentPage: 1,
    pageSize: 20,
    loading: false,
    
    // 当前设备详情
    currentComputer: null,
    currentComputerSoftware: [],
    detailLoading: false,
    
    // 最近更新的设备
    lastUpdatedComputers: [],
    
    // 错误信息
    error: null
  }),

  getters: {
    // 计算总页数
    totalPages: (state) => Math.ceil(state.totalComputers / state.pageSize),
    
    // 是否有设备数据
    hasComputers: (state) => state.computers.length > 0,
    
    // 当前设备是否有软件数据
    hasCurrentComputerSoftware: (state) => state.currentComputerSoftware.length > 0
  },

  actions: {
    // 获取设备列表
    async fetchComputers(start = 0, limit = 20) {
      this.loading = true
      this.error = null
      
      try {
        const response = await api.getComputers(start, limit)
        this.computers = response.data || []
        this.totalComputers = response.data?.length || 0
        this.currentPage = Math.floor(start / limit) + 1
        this.pageSize = limit
      } catch (error) {
        this.error = `获取设备列表失败: ${error.message}`
        console.error('Error fetching computers:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取单个设备详情
    async fetchComputerDetails(id) {
      this.detailLoading = true
      this.error = null
      
      try {
        const response = await api.getComputerDetails(id)
        this.currentComputer = response.data
      } catch (error) {
        this.error = `获取设备详情失败: ${error.message}`
        console.error('Error fetching computer details:', error)
      } finally {
        this.detailLoading = false
      }
    },

    // 获取设备软件列表
    async fetchComputerSoftware(computerId) {
      this.error = null
      
      try {
        const response = await api.getComputerSoftware(computerId)
        this.currentComputerSoftware = response.data || []
      } catch (error) {
        this.error = `获取软件列表失败: ${error.message}`
        console.error('Error fetching computer software:', error)
      }
    },

    // 获取最近更新的设备
    async fetchLastUpdatedComputers(timestamp) {
      this.error = null
      
      try {
        const response = await api.getLastUpdatedComputers(timestamp)
        this.lastUpdatedComputers = response.data || []
      } catch (error) {
        this.error = `获取最近更新设备失败: ${error.message}`
        console.error('Error fetching last updated computers:', error)
      }
    },

    // 清除当前设备信息
    clearCurrentComputer() {
      this.currentComputer = null
      this.currentComputerSoftware = []
    },

    // 清除错误信息
    clearError() {
      this.error = null
    },

    // 设置页面大小
    setPageSize(size) {
      this.pageSize = size
    }
  }
})
