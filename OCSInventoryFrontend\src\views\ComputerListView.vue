<template>
  <div class="computer-list-view">
    <!-- 页面标题和搜索 -->
    <div class="page-header">
      <h2>设备列表</h2>
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索设备名称或IP地址"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 设备列表表格 -->
    <el-card class="table-card">
      <el-table
        :data="filteredComputers"
        :loading="computerStore.loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无设备数据"
        @row-click="handleRowClick"
      >
        <el-table-column
          prop="id"
          label="设备ID"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="name"
          label="设备名称"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="operatingSystem"
          label="操作系统"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="ipAddress"
          label="IP地址"
          width="140"
          show-overflow-tooltip
        />
        <el-table-column
          prop="lastUpdate"
          label="最后更新时间"
          width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.lastUpdate">{{ formatDate(row.lastUpdate) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="viewDetails(row.id)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <Pagination
        :total="computerStore.totalComputers"
        :page="computerStore.currentPage"
        :size="computerStore.pageSize"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>

    <!-- 错误提示 -->
    <el-alert
      v-if="computerStore.error"
      :title="computerStore.error"
      type="error"
      show-icon
      closable
      @close="computerStore.clearError"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useComputerStore } from '../stores/computerStore'
import { Search, Refresh } from '@element-plus/icons-vue'
import Pagination from '../components/Pagination.vue'

const router = useRouter()
const computerStore = useComputerStore()

// 响应式数据
const searchKeyword = ref('')

// 计算属性 - 过滤后的设备列表
const filteredComputers = computed(() => {
  if (!searchKeyword.value) {
    return computerStore.computers
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return computerStore.computers.filter(computer => 
    computer.name?.toLowerCase().includes(keyword) ||
    computer.ipAddress?.toLowerCase().includes(keyword)
  )
})

// 方法
const refreshData = async () => {
  const start = (computerStore.currentPage - 1) * computerStore.pageSize
  await computerStore.fetchComputers(start, computerStore.pageSize)
}

const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里可以添加防抖逻辑
}

const handlePageChange = async (page) => {
  const start = (page - 1) * computerStore.pageSize
  await computerStore.fetchComputers(start, computerStore.pageSize)
}

const handleSizeChange = async (size) => {
  computerStore.setPageSize(size)
  await computerStore.fetchComputers(0, size)
}

const handleRowClick = (row) => {
  viewDetails(row.id)
}

const viewDetails = (id) => {
  router.push(`/computer/${id}`)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.computer-list-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-section {
  display: flex;
  gap: 10px;
  align-items: center;
}

.table-card {
  margin-bottom: 20px;
}

.el-table {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}
</style>
