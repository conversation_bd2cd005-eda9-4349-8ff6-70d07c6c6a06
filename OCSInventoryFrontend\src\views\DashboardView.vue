<template>
  <div class="dashboard-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>仪表盘</h2>
      <el-button @click="refreshData" type="primary">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#409eff"><Monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ computerStore.totalComputers }}</div>
              <div class="stat-label">设备总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#67c23a"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ onlineDevices }}</div>
              <div class="stat-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#e6a23c"><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ offlineDevices }}</div>
              <div class="stat-label">离线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#f56c6c"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ recentUpdates }}</div>
              <div class="stat-label">最近更新</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近更新的设备 -->
    <el-card class="recent-updates-card">
      <template #header>
        <div class="card-header">
          <span>最近更新的设备</span>
          <el-button type="text" @click="viewAllComputers">查看全部</el-button>
        </div>
      </template>
      
      <el-table
        :data="computerStore.lastUpdatedComputers.slice(0, 10)"
        :loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          prop="id"
          label="设备ID"
          width="100"
        />
        <el-table-column
          prop="name"
          label="设备名称"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="operatingSystem"
          label="操作系统"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="ipAddress"
          label="IP地址"
          width="140"
        />
        <el-table-column
          prop="lastUpdate"
          label="更新时间"
          width="180"
        >
          <template #default="{ row }">
            {{ formatDate(row.lastUpdate) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewDetails(row.id)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 快速操作 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快速操作</span>
      </template>
      
      <div class="quick-actions">
        <el-button type="primary" @click="viewAllComputers">
          <el-icon><List /></el-icon>
          查看所有设备
        </el-button>
        <el-button type="success" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </el-card>

    <!-- 错误提示 -->
    <el-alert
      v-if="computerStore.error"
      :title="computerStore.error"
      type="error"
      show-icon
      closable
      @close="computerStore.clearError"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useComputerStore } from '../stores/computerStore'
import { 
  Monitor, 
  CircleCheck, 
  Warning, 
  Clock, 
  Refresh, 
  List 
} from '@element-plus/icons-vue'

const router = useRouter()
const computerStore = useComputerStore()

// 响应式数据
const loading = ref(false)

// 计算属性
const onlineDevices = computed(() => {
  // 这里可以根据实际业务逻辑计算在线设备数
  return Math.floor(computerStore.totalComputers * 0.8)
})

const offlineDevices = computed(() => {
  return computerStore.totalComputers - onlineDevices.value
})

const recentUpdates = computed(() => {
  return computerStore.lastUpdatedComputers.length
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 获取基础设备数据
    await computerStore.fetchComputers(0, 20)
    
    // 获取最近更新的设备（假设获取最近24小时的）
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    await computerStore.fetchLastUpdatedComputers(yesterday.toISOString())
  } finally {
    loading.value = false
  }
}

const viewAllComputers = () => {
  router.push('/computers')
}

const viewDetails = (id) => {
  router.push(`/computer/${id}`)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.dashboard-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.recent-updates-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions-card {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 10px;
}
</style>
